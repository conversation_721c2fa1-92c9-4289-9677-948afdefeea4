{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">库存列表</div>
        <div class="layui-card-body">
            <!-- 库存统计卡片 -->
            <div class="layui-row layui-col-space15" style="margin-bottom: 20px;">

                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-weight: bold; color: #666;">
                            <i class="layui-icon layui-icon-app" style="margin-right: 8px;"></i>总商品数量
                        </div>
                        <div class="layui-card-body">
                            <h2 id="total-products" style="font-size: 24px; color: #666; margin-bottom: 10px;">{$stockStats.total_products}</h2>
                            <p style="color: #666;">普通商品: <span id="normal-products" style="color: #666; font-weight: bold;">{$stockStats.normal_product_count}</span> | 计量商品: <span id="weight-products" style="color: #666; font-weight: bold;">{$stockStats.weight_product_count}</span></p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-weight: bold; color: #666;">
                            <i class="layui-icon layui-icon-component" style="margin-right: 8px;"></i>普通商品总库存
                        </div>
                        <div class="layui-card-body">
                            <h2 id="total-normal-stock" style="font-size: 24px; color: #666; margin-bottom: 10px;">{$stockStats.total_normal_stock} <small style="font-size: 16px;">件</small></h2>
                            <p style="color: #666;">普通商品库存总计</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-weight: bold; color: #666;">
                            <i class="layui-icon layui-icon-chart-screen" style="margin-right: 8px;"></i>计量商品总重量
                        </div>
                        <div class="layui-card-body">
                            <h2 id="total-weight-stock" style="font-size: 24px; color: #666; margin-bottom: 10px;">{$stockStats.total_weight_stock} <small style="font-size: 16px;">kg</small></h2>
                            <p style="color: #666;">计量商品重量总计</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-weight: bold; color: #666;">
                            <i class="layui-icon layui-icon-cart" style="margin-right: 8px;"></i>总库存统计
                        </div>
                        <div class="layui-card-body">
                            <h2 id="total-stock-value" style="font-size: 24px; color: #666; margin-bottom: 10px;">{$stockStats.total_stock_value}</h2>
                            <p style="color: #666;">普通商品和计量商品总库存</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_search" id="product_search" autocomplete="off" class="layui-input" placeholder="搜索商品">
                            <input type="hidden" name="product_id" id="product_id">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">规格</label>
                        <div class="layui-input-inline">
                            <select name="inventory_id" id="inventory_id">
                                <option value="">全部规格</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">门店</label>
                        <div class="layui-input-inline">
                            <select name="shop_id" id="shop_id">
                                <option value="">全部</option>
                                {volist name="shops" id="shop"}
                                <option value="{$shop.id}">{$shop.title}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">商品类型</label>
                        <div class="layui-input-inline">
                            <select name="product_type" id="product_type">
                                <option value="">全部</option>
                                <option value="1">普通商品</option>
                                <option value="2">计量商品</option>
                                <option value="3">赠品</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">库存状态</label>
                        <div class="layui-input-inline">
                            <select name="stock_status" id="stock_status">
                                <option value="">全部</option>
                                <option value="1">有库存</option>
                                <option value="2">无库存</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                        <button class="layui-btn layui-btn-normal" id="export-btn">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>

            <!-- 商品图片模板 -->
            <script type="text/html" id="product-thumb">
                {{# if(d.product_thumb){ }}
                <img src="{{d.product_thumb}}" style="max-width: 50px; max-height: 50px;" onclick="showBigImage(this.src)">
                {{# } else { }}
                <img src="__STATIC__/admin/img/nopic.jpg" style="max-width: 50px; max-height: 50px;">
                {{# } }}
            </script>

            <!-- 商品类型模板 -->
            <script type="text/html" id="product-type-tpl">
                {{# if(d.product_type == 2){ }}
                <span class="layui-badge layui-bg-gray">计量商品</span>
                {{# } else if(d.product_type == 3){ }}
                <span class="layui-badge layui-bg-gray">赠品</span>
                {{# } else { }}
                <span class="layui-badge layui-bg-gray">普通商品</span>
                {{# } }}
            </script>

            <!-- 库存显示模板 -->
            <script type="text/html" id="stock-tpl">
                {{# if(d.product_type == 2){ }}
                    {{# if(d.weight_stock && d.weight_stock > 0){ }}
                    <span style="color: #666;">{{d.weight_stock}}kg</span>
                    {{# } else { }}
                    <span style="color: #999;">0kg</span>
                    {{# } }}
                {{# } else { }}
                    {{# if(d.stock && d.stock > 0){ }}
                    <span style="color: #666;">{{d.stock}}件</span>
                    {{# } else { }}
                    <span style="color: #999;">0件</span>
                    {{# } }}
                {{# } }}
            </script>
        </div>
    </div>
</div>

<script>
    function showBigImage(src) {
        // 显示大图
        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            area: ['auto'],
            skin: 'layui-layer-nobg',
            shadeClose: true,
            content: '<img src="' + src + '" style="max-width: 100%; max-height: 500px;">'
        });
    }

    layui.use(['table', 'form', 'laydate'], function() {
        var table = layui.table,
            form = layui.form,
            $ = layui.jquery;

        // 表格实例
        table.render({
            elem: '#data-table',
            url: '{:url("inventory/stock")}',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'title', title: '所属门店'},
                {field: 'product_thumb', title: '商品图片', templet: '#product-thumb'},
                {field: 'product_name', title: '商品名称'},
                {field: 'product_type', title: '商品类型', templet: '#product-type-tpl', width: 120},
                {field: 'inventory_name', title: '规格名称'},
                {field: 'stock', title: '库存', templet: '#stock-tpl', width: 120},
                {field: 'add_time', title: '创建时间', sort: true},
                {field: 'update_time', title: '更新时间', sort: true}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 商品自动完成功能
        $('#product_search').on('input', function() {
            var keyword = $(this).val();
            if (keyword.length >= 1) {
                $.ajax({
                    url: '{:url("inventory/searchProduct")}',
                    type: 'post',
                    data: {keyword: keyword},
                    success: function(res) {
                        if (res.code === 0 && res.data.length > 0) {
                            var html = '';
                            for (var i = 0; i < res.data.length; i++) {
                                html += '<div class="autocomplete-item" data-id="' + res.data[i].id + '">' + res.data[i].name + '</div>';
                            }
                            showAutocomplete(html);
                        } else {
                            hideAutocomplete();
                        }
                    }
                });
            } else {
                hideAutocomplete();
                $('#product_id').val('');
                // 清空规格下拉框
                $('#inventory_id').html('<option value="">全部规格</option>');
                form.render('select');
            }
        });

        // 显示自动完成结果
        function showAutocomplete(html) {
            var $input = $('#product_search');
            var offset = $input.offset();
            var width = $input.outerWidth();

            // 移除已有的自动完成框
            $('.autocomplete-dropdown').remove();

            // 创建新的自动完成框
            var dropdown = $('<div class="autocomplete-dropdown"></div>').css({
                position: 'absolute',
                top: offset.top + $input.outerHeight(),
                left: offset.left,
                width: width,
                'max-height': '200px',
                'overflow-y': 'auto',
                'background-color': '#fff',
                'border': '1px solid #ddd',
                'z-index': 999
            }).html(html);

            $('body').append(dropdown);

            // 绑定点击事件
            $('.autocomplete-item').on('click', function() {
                var id = $(this).data('id');
                var name = $(this).text();
                $input.val(name);
                $('#product_id').val(id);
                hideAutocomplete();

                // 加载该商品的规格
                loadInventories(id);
            });
        }

        // 隐藏自动完成结果
        function hideAutocomplete() {
            $('.autocomplete-dropdown').remove();
        }

        // 点击其他区域隐藏自动完成
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#product_search, .autocomplete-dropdown').length) {
                hideAutocomplete();
            }
        });

        // 加载商品规格
        function loadInventories(productId) {
            $.ajax({
                url: '{:url("inventory/getInventoryByProduct")}',
                type: 'post',
                data: {product_id: productId},
                success: function(res) {
                    if (res.code === 0) {
                        var html = '<option value="">全部规格</option>';
                        for (var i = 0; i < res.data.length; i++) {
                            html += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
                        }
                        $('#inventory_id').html(html);
                        form.render('select');
                    }
                }
            });
        }

        // 更新库存统计卡片
        function updateStockStats() {
            var param = {
                product_id: $('#product_id').val(),
                inventory_id: $('#inventory_id').val(),
                shop_id: $('#shop_id').val(),
                product_type: $('#product_type').val(),
                stock_status: $('#stock_status').val(),
                get_stats: 1 // 标记获取统计数据
            };

            $.ajax({
                url: '{:url("inventory/getStockStats")}',
                type: 'post',
                data: param,
                success: function(res) {
                    if (res.code === 0) {
                        // 使用ID选择器更新统计数据，更加稳健
                        $('#total-stock-value').html(res.data.total_stock_value);
                        $('#total-products').html(res.data.total_products);
                        $('#normal-products').html(res.data.normal_product_count);
                        $('#weight-products').html(res.data.weight_product_count);
                        $('#total-normal-stock').html(res.data.total_normal_stock + ' <small style="font-size: 16px; color: #666;">件</small>');
                        $('#total-weight-stock').html(res.data.total_weight_stock + ' <small style="font-size: 16px; color: #666;">kg</small>');
                    }
                }
            });
        }

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                product_id: $('#product_id').val(),
                inventory_id: $('#inventory_id').val(),
                shop_id: $('#shop_id').val(),
                product_type: $('#product_type').val(),
                stock_status: $('#stock_status').val()
            };
            table.reload('data-table', {
                where: param,
                page: {
                    curr: 1
                }
            });

            // 更新库存统计卡片
            updateStockStats();
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#product_search').val('');
            $('#product_id').val('');
            $('#inventory_id').html('<option value="">全部规格</option>');
            $('#shop_id').val('');
            $('#product_type').val('');
            $('#stock_status').val('');
            form.render('select');
            $('#search-btn').click();
        });

        // 导出按钮点击事件
        $('#export-btn').on('click', function() {
            var param = {
                product_id: $('#product_id').val(),
                inventory_id: $('#inventory_id').val(),
                shop_id: $('#shop_id').val(),
                product_type: $('#product_type').val(),
                stock_status: $('#stock_status').val()
            };

            // 构建导出URL
            var exportUrl = '{:url("inventory/export_stock")}' +
                '?product_id=' + encodeURIComponent(param.product_id) +
                '&inventory_id=' + encodeURIComponent(param.inventory_id) +
                '&shop_id=' + encodeURIComponent(param.shop_id) +
                '&product_type=' + encodeURIComponent(param.product_type) +
                '&stock_status=' + encodeURIComponent(param.stock_status);

            // 跳转到导出页面
            window.location.href = exportUrl;
        });
    });
</script>

<style>
.autocomplete-item {
    padding: 8px 10px;
    cursor: pointer;
}
.autocomplete-item:hover {
    background-color: #f2f2f2;
}

/* 库存卡片样式 */
.layui-card {
    transition: all 0.3s ease;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
.layui-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.layui-card-header {
    border-bottom: 1px solid #f2f2f2;
    font-size: 16px;
    padding: 12px 15px;
}
.layui-card-body {
    padding: 15px;
}
</style>
